/* Otimizações de Performance */
.mobile-device .link-button::before,
.mobile-device .link-button::after,
.mobile-device .link-item::before,
.mobile-device .link-item::after,
.mobile-device .floating-particle {
    display: none !important;
}

.low-performance *,
.reduce-animations * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
    animation-iteration-count: 1 !important;
}

.low-performance .link-button:hover,
.low-performance .link-item:hover,
.reduce-animations .link-button:hover,
.reduce-animations .link-item:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.low-performance .config-modal-content {
    backdrop-filter: none !important;
    background: var(--primary-color) !important;
}

.reduce-animations .link-button::before,
.reduce-animations .link-button::after,
.reduce-animations .link-item::before,
.reduce-animations .link-item::after {
    animation: none !important;
}

/* Classes Utilitárias Responsivas */
.mobile-only { display: none; }
.desktop-only { display: block; }

/* ===== OTIMIZAÇÕES TOUCH ===== */

.touch-device button,
.touch-device .link-item,
.touch-device .toggle-switch {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.touch-device button:active {
    transform: scale(0.98);
}

/* ===== CHOICES.JS CUSTOM STYLES ===== */

.choices-custom .choices__list--dropdown {
    background-color: var(--secondary-color);
    border-color: var(--accent-color);
}

.choices-custom .choices__item {
    color: var(--text-light);
}

.choices-custom .choices__item--choice {
    background-color: var(--secondary-color);
}

.choices-custom .choices__item--choice.is-highlighted {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

/* Estilos específicos para as classes customizadas */
.choices-custom__list {
    background-color: var(--secondary-color);
    border: 1px solid #333;
    border-radius: 8px;
}

.choices-custom__list--dropdown {
    background-color: var(--secondary-color);
    border: 1px solid var(--accent-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.choices-custom__item {
    color: var(--text-light);
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.choices-custom__item--choice {
    background-color: var(--secondary-color);
    color: var(--text-light);
    cursor: pointer;
}

.choices-custom__item--choice:hover,
.choices-custom__item--choice.is-highlighted {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

/* ===== ACESSIBILIDADE ===== */

/* Foco visível melhorado */
.tab-btn:focus,
button:focus,
input:focus,
select:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Navegação por teclado */
.tab-btn:focus-visible {
    background: rgba(212, 175, 55, 0.2);
}

/* Screen reader */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== MELHORIAS DE CONTRASTE ===== */

@media (prefers-contrast: high) {
    .tab-btn {
        border: 1px solid var(--text-gray);
    }

    .tab-btn.active {
        border-color: var(--accent-color);
        background: var(--accent-color);
        color: var(--primary-color);
    }

    .link-item {
        border: 1px solid var(--text-gray);
    }
}

/* ===== MODO ESCURO/CLARO ===== */

@media (prefers-color-scheme: light) {
    .config-modal[data-theme="auto"] {
        --primary-color: #ffffff;
        --secondary-color: #f5f5f5;
        --text-light: #333333;
        --text-gray: #666666;
    }
}

/* ===== MAGIC UI EFFECTS ===== */

/* Partículas flutuantes */
.floating-particle {
    position: fixed !important;
    width: 4px !important;
    height: 4px !important;
    background: var(--accent-color) !important;
    border-radius: 50% !important;
    pointer-events: none !important;
    z-index: 1 !important;
    opacity: 0.6 !important;
}
